<?php
/**
 * Book Consultation Page Template for BALLERY.IN
 * 
 * @package BALLERY.IN
 */

get_header(); ?>

<section class="section" style="padding-top: calc(var(--spacing-xxl) + 60px);">
    <div class="container">
        <div class="grid grid-2" style="gap: var(--spacing-xxl); align-items: start;">
            <!-- Main Booking Form -->
            <div>
                <div class="form-header mb-4">
                    <h1>Book Your Consultation</h1>
                    <p>Fill out the form below to schedule your professional construction consultation. Our experts will review your requirements and confirm your appointment.</p>
                </div>
                
                <form id="consultation-booking-form" class="consultation-form">
                    <!-- Service Type Selection -->
                    <div class="form-group">
                        <label for="service-type" class="form-label">Type of Service *</label>
                        <select id="service-type" name="service_type" class="form-select" required>
                            <option value="">Select service type</option>
                            <option value="virtual" <?php echo (isset($_GET['type']) && $_GET['type'] === 'virtual') ? 'selected' : ''; ?>>Virtual Consultation</option>
                            <option value="site-visit" <?php echo (isset($_GET['type']) && $_GET['type'] === 'site-visit') ? 'selected' : ''; ?>>Site Visit</option>
                        </select>
                    </div>
                    
                    <!-- Dynamic Service Selection -->
                    <div class="form-group">
                        <label class="form-label">Select Service(s) *</label>
                        <div id="service-options" class="form-checkbox-group">
                            <!-- Services will be populated dynamically based on service type -->
                        </div>
                    </div>
                    
                    <!-- Personal Information -->
                    <div class="grid grid-2" style="gap: var(--spacing-md);">
                        <div class="form-group">
                            <label for="client-name" class="form-label">Full Name *</label>
                            <input type="text" id="client-name" name="name" class="form-input" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="client-phone" class="form-label">Phone Number *</label>
                            <input type="tel" id="client-phone" name="phone" class="form-input" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="client-email" class="form-label">Email Address *</label>
                        <input type="email" id="client-email" name="email" class="form-input" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="site-location" class="form-label">Site Location *</label>
                        <input type="text" id="site-location" name="location" class="form-input" placeholder="City, State" required>
                    </div>
                    
                    <!-- Project Details -->
                    <div class="form-group">
                        <label for="project-description" class="form-label">Description of Problem/Requirement *</label>
                        <textarea id="project-description" name="description" class="form-textarea" placeholder="Please describe your project stage, specific concerns, or what help you need..." required></textarea>
                    </div>
                    
                    <!-- File Upload -->
                    <div class="form-group">
                        <label for="project-files" class="form-label">Upload Drawings/Images (Optional)</label>
                        <div class="form-file">
                            <input type="file" id="project-files" name="files[]" multiple accept=".pdf,.jpg,.jpeg,.png,.dwg">
                            <p>Drag and drop files here or click to browse<br>
                            <small>Supported formats: PDF, JPG, PNG, DWG (Max 10MB per file)</small></p>
                        </div>
                    </div>
                    
                    <!-- Preferred Date and Time -->
                    <div class="form-group">
                        <label class="form-label">Preferred Date and Time *</label>
                        <div class="grid grid-2" style="gap: var(--spacing-md);">
                            <div>
                                <label for="preferred-date" class="form-label">Date</label>
                                <input type="date" id="preferred-date" name="preferred_date" class="form-input" required min="<?php echo date('Y-m-d', strtotime('+1 day')); ?>">
                            </div>
                            <div>
                                <label for="preferred-time" class="form-label">Time</label>
                                <select id="preferred-time" name="preferred_time" class="form-select" required>
                                    <option value="">Select time</option>
                                    <option value="09:00">9:00 AM</option>
                                    <option value="10:00">10:00 AM</option>
                                    <option value="11:00">11:00 AM</option>
                                    <option value="12:00">12:00 PM</option>
                                    <option value="14:00">2:00 PM</option>
                                    <option value="15:00">3:00 PM</option>
                                    <option value="16:00">4:00 PM</option>
                                    <option value="17:00">5:00 PM</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Terms and Conditions -->
                    <div class="form-group">
                        <div class="form-checkbox">
                            <input type="checkbox" id="terms-agreement" name="terms" required>
                            <label for="terms-agreement">I agree to the <a href="<?php echo home_url('/terms-of-service'); ?>" target="_blank">Terms of Service</a> and <a href="<?php echo home_url('/privacy-policy'); ?>" target="_blank">Privacy Policy</a> *</label>
                        </div>
                    </div>
                    
                    <!-- Submit Button -->
                    <button type="submit" class="btn btn-primary form-submit">Request My Consultation</button>
                </form>
                
                <!-- Success Message (Hidden by default) -->
                <div id="booking-success" class="hidden" style="background: var(--success-green); color: white; padding: var(--spacing-lg); border-radius: var(--border-radius); margin-top: var(--spacing-lg);">
                    <h3>Booking Request Submitted Successfully!</h3>
                    <p>Thank you for choosing BALLERY.IN. We have received your consultation request and will contact you within 2 hours to confirm your appointment and discuss next steps.</p>
                    <p><strong>What happens next:</strong></p>
                    <ul>
                        <li>Our team will review your requirements</li>
                        <li>We'll call you to confirm the appointment</li>
                        <li>You'll receive a confirmation email with details</li>
                        <li>Payment link will be shared for booking confirmation</li>
                    </ul>
                </div>
            </div>
            
            <!-- Trust Bar / Sidebar -->
            <div>
                <div class="card" style="position: sticky; top: 100px;">
                    <div class="card-header">
                        <h3>Why Choose BALLERY.IN?</h3>
                    </div>
                    <div class="card-body">
                        <div style="margin-bottom: var(--spacing-lg);">
                            <div style="display: flex; align-items: center; gap: var(--spacing-sm); margin-bottom: var(--spacing-sm);">
                                <span style="color: var(--success-green); font-size: 1.5rem;">🔒</span>
                                <strong>100% Secure & Confidential</strong>
                            </div>
                            <p style="font-size: 0.875rem; color: var(--neutral-gray);">Your project details and personal information are completely secure with us.</p>
                        </div>
                        
                        <div style="margin-bottom: var(--spacing-lg);">
                            <div style="display: flex; align-items: center; gap: var(--spacing-sm); margin-bottom: var(--spacing-sm);">
                                <span style="color: var(--success-green); font-size: 1.5rem;">⚡</span>
                                <strong>Quick Response Time</strong>
                            </div>
                            <p style="font-size: 0.875rem; color: var(--neutral-gray);">We respond to all booking requests within 2 hours during business hours.</p>
                        </div>
                        
                        <div style="margin-bottom: var(--spacing-lg);">
                            <div style="display: flex; align-items: center; gap: var(--spacing-sm); margin-bottom: var(--spacing-sm);">
                                <span style="color: var(--success-green); font-size: 1.5rem;">👨‍💼</span>
                                <strong>Expert Consultants</strong>
                            </div>
                            <p style="font-size: 0.875rem; color: var(--neutral-gray);">15+ years of experience in residential construction projects.</p>
                        </div>
                        
                        <div style="border-top: 1px solid var(--border-light); padding-top: var(--spacing-md);">
                            <div style="background: var(--neutral-gray-light); padding: var(--spacing-md); border-radius: var(--border-radius); border-left: 4px solid var(--accent-orange);">
                                <p style="font-style: italic; margin-bottom: var(--spacing-xs);">"Professional service that saved us from costly mistakes. Highly recommended!"</p>
                                <div style="font-size: 0.875rem; color: var(--neutral-gray);">
                                    <strong>Priya S.</strong> - Bangalore
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <div style="text-align: center;">
                            <p style="margin-bottom: var(--spacing-sm);"><strong>Need Help?</strong></p>
                            <div style="display: flex; gap: var(--spacing-sm); justify-content: center;">
                                <a href="tel:+919876543210" class="btn btn-sm btn-secondary">Call Us</a>
                                <a href="https://wa.me/919876543210" class="btn btn-sm btn-primary" target="_blank">WhatsApp</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Initialize form when page loads
document.addEventListener('DOMContentLoaded', function() {
    const serviceTypeSelect = document.getElementById('service-type');
    const serviceOptionsContainer = document.getElementById('service-options');
    
    // Initialize with URL parameter if present
    if (serviceTypeSelect.value) {
        updateServiceOptions(serviceTypeSelect.value);
    }
    
    // Handle service type change
    serviceTypeSelect.addEventListener('change', function() {
        updateServiceOptions(this.value);
    });
    
    function updateServiceOptions(serviceType) {
        const virtualServices = [
            { id: 'drawing-evaluation', name: 'Drawing Evaluation', price: '₹1,499' },
            { id: 'agreement-verification', name: 'Agreement Verification', price: '₹999' },
            { id: 'boq-preparation', name: 'BoQ Preparation', price: '₹2,499' },
            { id: 'structural-consultation', name: 'Structural Consultation', price: '₹1,999' },
            { id: 'material-specification', name: 'Material Specification Review', price: '₹1,299' },
            { id: 'cost-estimation', name: 'Cost Estimation', price: '₹1,799' }
        ];
        
        const siteVisitServices = [
            { id: 'site-marking-check', name: 'Site Marking Check', price: '₹2,999' },
            { id: 'steel-reinforcement-check', name: 'Steel Reinforcement Check', price: '₹3,499' },
            { id: 'concreting-check', name: 'Concreting Check', price: '₹4,499' },
            { id: 'measurements-billing-check', name: 'Measurements & Billing Check', price: '₹2,499' },
            { id: 'quality-inspection', name: 'Quality Inspection', price: '₹3,999' },
            { id: 'safety-audit', name: 'Safety Audit', price: '₹2,799' }
        ];
        
        const services = serviceType === 'virtual' ? virtualServices : siteVisitServices;
        
        serviceOptionsContainer.innerHTML = '';
        
        if (serviceType) {
            services.forEach(service => {
                const checkboxDiv = document.createElement('div');
                checkboxDiv.className = 'form-checkbox';
                checkboxDiv.innerHTML = `
                    <input type="checkbox" id="service-${service.id}" name="services[]" value="${service.name}">
                    <label for="service-${service.id}">${service.name} <span style="color: var(--neutral-gray); font-size: 0.875rem;">(${service.price})</span></label>
                `;
                serviceOptionsContainer.appendChild(checkboxDiv);
            });
        }
    }
    
    // Handle form submission
    document.getElementById('consultation-booking-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Show success message
        document.getElementById('booking-success').classList.remove('hidden');
        this.style.display = 'none';
        
        // Scroll to success message
        document.getElementById('booking-success').scrollIntoView({ behavior: 'smooth' });
    });
});
</script>

<?php get_footer(); ?>
