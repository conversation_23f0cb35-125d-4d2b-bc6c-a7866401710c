/**
 * BALLERY.IN Main JavaScript
 * 
 * @package BALLERY.IN
 * @version 1.0
 */

(function() {
    'use strict';
    
    // DOM Ready
    document.addEventListener('DOMContentLoaded', function() {
        initializeComponents();
    });
    
    /**
     * Initialize all components
     */
    function initializeComponents() {
        initMobileMenu();
        initScrollEffects();
        initSmoothScrolling();
        initFAQAccordion();
        initTabSystem();
        initFormHandling();
        initTestimonialCarousel();
        initAnimations();
    }
    
    /**
     * Mobile Menu Functionality
     */
    function initMobileMenu() {
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const mainNav = document.getElementById('main-nav');

        if (mobileMenuToggle && mainNav) {
            // Mobile menu toggle functionality
            mobileMenuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                mainNav.classList.toggle('active');
                const isOpen = mainNav.classList.contains('active');

                // Update button state
                mobileMenuToggle.setAttribute('aria-expanded', isOpen);
                mobileMenuToggle.innerHTML = isOpen ? '<span>✕</span>' : '<span>☰</span>';

                // Prevent body scroll when menu is open
                if (isOpen) {
                    document.body.style.overflow = 'hidden';
                } else {
                    document.body.style.overflow = '';
                }
            });

            // Close menu when clicking outside
            document.addEventListener('click', function(e) {
                if (!mainNav.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                    closeMenu();
                }
            });

            // Close menu when clicking on menu links
            mainNav.querySelectorAll('a').forEach(link => {
                link.addEventListener('click', function() {
                    closeMenu();
                });
            });

            // Close menu on window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 576) {
                    closeMenu();
                }
            });

            function closeMenu() {
                mainNav.classList.remove('active');
                mobileMenuToggle.setAttribute('aria-expanded', 'false');
                mobileMenuToggle.innerHTML = '<span>☰</span>';
                document.body.style.overflow = '';
            }
        }
    }
    
    /**
     * Scroll Effects
     */
    function initScrollEffects() {
        const siteHeader = document.getElementById('site-header');
        let lastScrollTop = 0;
        
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            // Header scroll effect
            if (scrollTop > 100) {
                siteHeader.classList.add('scrolled');
            } else {
                siteHeader.classList.remove('scrolled');
            }
            
            // Animate elements on scroll
            animateOnScroll();
            
            lastScrollTop = scrollTop;
        });
    }
    
    /**
     * Smooth Scrolling for Anchor Links
     */
    function initSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const headerHeight = document.getElementById('site-header').offsetHeight;
                    const targetPosition = target.offsetTop - headerHeight - 20;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            });
        });
    }
    
    /**
     * FAQ Accordion
     */
    function initFAQAccordion() {
        document.querySelectorAll('.faq-question').forEach(question => {
            question.addEventListener('click', function() {
                const faqItem = this.parentElement;
                const isActive = faqItem.classList.contains('active');
                
                // Close all FAQ items
                document.querySelectorAll('.faq-item').forEach(item => {
                    item.classList.remove('active');
                });
                
                // Open clicked item if it wasn't active
                if (!isActive) {
                    faqItem.classList.add('active');
                }
            });
        });
    }
    
    /**
     * Tab System
     */
    function initTabSystem() {
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', function() {
                const tabsContainer = this.closest('.tabs-container');
                const targetTab = this.getAttribute('data-tab');
                
                // Remove active class from all buttons and content
                tabsContainer.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                tabsContainer.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
                
                // Add active class to clicked button and corresponding content
                this.classList.add('active');
                const targetContent = tabsContainer.querySelector(`[data-tab-content="${targetTab}"]`);
                if (targetContent) {
                    targetContent.classList.add('active');
                }
            });
        });
    }
    
    /**
     * Form Handling
     */
    function initFormHandling() {
        // Dynamic service selection in booking form
        const serviceTypeSelect = document.getElementById('service-type');
        const serviceOptionsContainer = document.getElementById('service-options');
        
        if (serviceTypeSelect && serviceOptionsContainer) {
            serviceTypeSelect.addEventListener('change', function() {
                updateServiceOptions(this.value);
            });
            
            // Initialize with URL parameter if present
            if (serviceTypeSelect.value) {
                updateServiceOptions(serviceTypeSelect.value);
            }
        }
        
        // Form submissions
        initFormSubmissions();
    }
    
    /**
     * Update Service Options
     */
    function updateServiceOptions(serviceType) {
        const serviceOptionsContainer = document.getElementById('service-options');
        if (!serviceOptionsContainer) return;
        
        const virtualServices = [
            { id: 'drawing-evaluation', name: 'Drawing Evaluation', price: '₹1,499' },
            { id: 'agreement-verification', name: 'Agreement Verification', price: '₹999' },
            { id: 'boq-preparation', name: 'BoQ Preparation', price: '₹2,499' },
            { id: 'structural-consultation', name: 'Structural Consultation', price: '₹1,999' },
            { id: 'material-specification', name: 'Material Specification Review', price: '₹1,299' },
            { id: 'cost-estimation', name: 'Cost Estimation', price: '₹1,799' }
        ];
        
        const siteVisitServices = [
            { id: 'site-marking-check', name: 'Site Marking Check', price: '₹2,999' },
            { id: 'steel-reinforcement-check', name: 'Steel Reinforcement Check', price: '₹3,499' },
            { id: 'concreting-check', name: 'Concreting Check', price: '₹4,499' },
            { id: 'measurements-billing-check', name: 'Measurements & Billing Check', price: '₹2,499' },
            { id: 'quality-inspection', name: 'Quality Inspection', price: '₹3,999' },
            { id: 'safety-audit', name: 'Safety Audit', price: '₹2,799' }
        ];
        
        const services = serviceType === 'virtual' ? virtualServices : siteVisitServices;
        
        serviceOptionsContainer.innerHTML = '';
        
        if (serviceType) {
            services.forEach(service => {
                const checkboxDiv = document.createElement('div');
                checkboxDiv.className = 'form-checkbox';
                checkboxDiv.innerHTML = `
                    <input type="checkbox" id="service-${service.id}" name="services[]" value="${service.name}">
                    <label for="service-${service.id}">${service.name} <span style="color: var(--neutral-gray); font-size: 0.875rem;">(${service.price})</span></label>
                `;
                serviceOptionsContainer.appendChild(checkboxDiv);
            });
        }
    }
    
    /**
     * Form Submissions
     */
    function initFormSubmissions() {
        // Consultation booking form
        const bookingForm = document.getElementById('consultation-booking-form');
        if (bookingForm) {
            bookingForm.addEventListener('submit', function(e) {
                e.preventDefault();
                handleBookingSubmission(this);
            });
        }
        
        // Contact form
        const contactForm = document.getElementById('contact-form');
        if (contactForm) {
            contactForm.addEventListener('submit', function(e) {
                e.preventDefault();
                handleContactSubmission(this);
            });
        }
        
        // Callback form
        const callbackForm = document.getElementById('callback-form');
        if (callbackForm) {
            callbackForm.addEventListener('submit', function(e) {
                e.preventDefault();
                handleCallbackSubmission(this);
            });
        }
    }
    
    /**
     * Handle Booking Form Submission
     */
    function handleBookingSubmission(form) {
        const successMessage = document.getElementById('booking-success');
        if (successMessage) {
            successMessage.classList.remove('hidden');
            form.style.display = 'none';
            successMessage.scrollIntoView({ behavior: 'smooth' });
        }
    }
    
    /**
     * Handle Contact Form Submission
     */
    function handleContactSubmission(form) {
        const successMessage = document.getElementById('contact-success');
        if (successMessage) {
            successMessage.classList.remove('hidden');
            form.style.display = 'none';
            successMessage.scrollIntoView({ behavior: 'smooth' });
        }
    }
    
    /**
     * Handle Callback Form Submission
     */
    function handleCallbackSubmission(form) {
        // Show a simple success message
        alert('Thank you! We will call you back within 2 hours.');
        form.reset();
    }
    
    /**
     * Testimonial Carousel
     */
    function initTestimonialCarousel() {
        const testimonialTrack = document.querySelector('.testimonial-track');
        const testimonialDots = document.querySelectorAll('.carousel-dot');
        let currentSlide = 0;
        
        if (testimonialTrack && testimonialDots.length > 0) {
            testimonialDots.forEach((dot, index) => {
                dot.addEventListener('click', () => {
                    currentSlide = index;
                    updateCarousel();
                });
            });
            
            function updateCarousel() {
                testimonialTrack.style.transform = `translateX(-${currentSlide * 100}%)`;
                testimonialDots.forEach((dot, index) => {
                    dot.classList.toggle('active', index === currentSlide);
                });
            }
            
            // Auto-advance carousel
            setInterval(() => {
                currentSlide = (currentSlide + 1) % testimonialDots.length;
                updateCarousel();
            }, 5000);
        }
    }
    
    /**
     * Animate elements on scroll
     */
    function animateOnScroll() {
        const elements = document.querySelectorAll('.fade-in-up, .fade-in');
        
        elements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const elementVisible = 150;
            
            if (elementTop < window.innerHeight - elementVisible) {
                element.classList.add('animate');
            }
        });
    }
    
    /**
     * Initialize animations
     */
    function initAnimations() {
        // Add animation classes to elements
        const heroContent = document.querySelector('.hero-content');
        if (heroContent) {
            heroContent.classList.add('fade-in-up');
        }
        
        const serviceCards = document.querySelectorAll('.service-card');
        serviceCards.forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('fade-in-up');
            }, index * 100);
        });
    }
    
    /**
     * Utility Functions
     */
    function debounce(func, wait, immediate) {
        let timeout;
        return function executedFunction() {
            const context = this;
            const args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }
    
})();
