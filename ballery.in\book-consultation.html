<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Consultation - BALLERY.IN</title>
    <meta name="description" content="Book your professional construction consultation. Virtual consultations and on-site inspections available. Expert advice for your residential project.">
    
    <!-- Preconnect to Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Main Stylesheet -->
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <header class="site-header" id="site-header">
        <div class="container">
            <nav class="navbar">
                <a href="index.html" class="site-logo">BALLERY.IN</a>
                <ul class="main-nav" id="main-nav">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="services.html">Services</a></li>
                    <li><a href="pricing.html">Pricing</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                    <li><a href="book-consultation.html" class="btn btn-primary btn-sm active">Book Now</a></li>
                </ul>
                <button class="mobile-menu-toggle" id="mobile-menu-toggle" aria-label="Toggle mobile menu">
                    <span>☰</span>
                </button>
            </nav>
        </div>
    </header>

    <main id="main-content">
        <section class="section" style="padding-top: calc(var(--spacing-xxl) + 60px);">
            <div class="container">
                <div class="grid grid-2" style="gap: var(--spacing-xxl); align-items: start;">
                    <!-- Main Booking Form -->
                    <div>
                        <div class="form-header mb-4">
                            <h1>Book Your Consultation</h1>
                            <p>Fill out the form below to schedule your professional construction consultation. Our experts will review your requirements and confirm your appointment.</p>
                        </div>
                        
                        <form id="consultation-booking-form" class="consultation-form">
                            <!-- Service Type Selection -->
                            <div class="form-group">
                                <label for="service-type" class="form-label">Type of Service *</label>
                                <select id="service-type" name="service_type" class="form-select" required>
                                    <option value="">Select service type</option>
                                    <option value="virtual">Virtual Consultation</option>
                                    <option value="site-visit">Site Visit</option>
                                </select>
                            </div>
                            
                            <!-- Dynamic Service Selection -->
                            <div class="form-group">
                                <label class="form-label">Select Service(s) *</label>
                                <div id="service-options" class="form-checkbox-group">
                                    <!-- Services will be populated dynamically based on service type -->
                                </div>
                            </div>
                            
                            <!-- Personal Information -->
                            <div class="grid grid-2" style="gap: var(--spacing-md);">
                                <div class="form-group">
                                    <label for="client-name" class="form-label">Full Name *</label>
                                    <input type="text" id="client-name" name="name" class="form-input" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="client-phone" class="form-label">Phone Number *</label>
                                    <input type="tel" id="client-phone" name="phone" class="form-input" required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="client-email" class="form-label">Email Address *</label>
                                <input type="email" id="client-email" name="email" class="form-input" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="site-location" class="form-label">Site Location *</label>
                                <input type="text" id="site-location" name="location" class="form-input" placeholder="City, State" required>
                            </div>
                            
                            <!-- Project Details -->
                            <div class="form-group">
                                <label for="project-description" class="form-label">Description of Problem/Requirement *</label>
                                <textarea id="project-description" name="description" class="form-textarea" placeholder="Please describe your project stage, specific concerns, or what help you need..." required></textarea>
                            </div>
                            
                            <!-- File Upload -->
                            <div class="form-group">
                                <label for="project-files" class="form-label">Upload Drawings/Images (Optional)</label>
                                <div class="form-file">
                                    <input type="file" id="project-files" name="files[]" multiple accept=".pdf,.jpg,.jpeg,.png,.dwg">
                                    <p>Drag and drop files here or click to browse<br>
                                    <small>Supported formats: PDF, JPG, PNG, DWG (Max 10MB per file)</small></p>
                                </div>
                            </div>
                            
                            <!-- Preferred Date and Time -->
                            <div class="form-group">
                                <label class="form-label">Preferred Date and Time *</label>
                                <div class="grid grid-2" style="gap: var(--spacing-md);">
                                    <div>
                                        <label for="preferred-date" class="form-label">Date</label>
                                        <input type="date" id="preferred-date" name="preferred_date" class="form-input" required>
                                    </div>
                                    <div>
                                        <label for="preferred-time" class="form-label">Time</label>
                                        <select id="preferred-time" name="preferred_time" class="form-select" required>
                                            <option value="">Select time</option>
                                            <option value="09:00">9:00 AM</option>
                                            <option value="10:00">10:00 AM</option>
                                            <option value="11:00">11:00 AM</option>
                                            <option value="12:00">12:00 PM</option>
                                            <option value="14:00">2:00 PM</option>
                                            <option value="15:00">3:00 PM</option>
                                            <option value="16:00">4:00 PM</option>
                                            <option value="17:00">5:00 PM</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Terms and Conditions -->
                            <div class="form-group">
                                <div class="form-checkbox">
                                    <input type="checkbox" id="terms-agreement" name="terms" required>
                                    <label for="terms-agreement">I agree to the Terms of Service and Privacy Policy *</label>
                                </div>
                            </div>
                            
                            <!-- Submit Button -->
                            <button type="submit" class="btn btn-primary form-submit">Request My Consultation</button>
                        </form>
                        
                        <!-- Success Message (Hidden by default) -->
                        <div id="booking-success" class="hidden" style="background: var(--success-green); color: white; padding: var(--spacing-lg); border-radius: var(--border-radius); margin-top: var(--spacing-lg);">
                            <h3>Booking Request Submitted Successfully!</h3>
                            <p>Thank you for choosing BALLERY.IN. We have received your consultation request and will contact you within 2 hours to confirm your appointment and discuss next steps.</p>
                            <p><strong>What happens next:</strong></p>
                            <ul>
                                <li>Our team will review your requirements</li>
                                <li>We'll call you to confirm the appointment</li>
                                <li>You'll receive a confirmation email with details</li>
                                <li>Payment link will be shared for booking confirmation</li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- Trust Bar / Sidebar -->
                    <div>
                        <div class="card" style="position: sticky; top: 100px;">
                            <div class="card-header">
                                <h3>Why Choose BALLERY.IN?</h3>
                            </div>
                            <div class="card-body">
                                <div style="margin-bottom: var(--spacing-lg);">
                                    <div style="display: flex; align-items: center; gap: var(--spacing-sm); margin-bottom: var(--spacing-sm);">
                                        <span style="color: var(--success-green); font-size: 1.5rem;">🔒</span>
                                        <strong>100% Secure & Confidential</strong>
                                    </div>
                                    <p style="font-size: 0.875rem; color: var(--neutral-gray);">Your project details and personal information are completely secure with us.</p>
                                </div>
                                
                                <div style="margin-bottom: var(--spacing-lg);">
                                    <div style="display: flex; align-items: center; gap: var(--spacing-sm); margin-bottom: var(--spacing-sm);">
                                        <span style="color: var(--success-green); font-size: 1.5rem;">⚡</span>
                                        <strong>Quick Response Time</strong>
                                    </div>
                                    <p style="font-size: 0.875rem; color: var(--neutral-gray);">We respond to all booking requests within 2 hours during business hours.</p>
                                </div>
                                
                                <div style="margin-bottom: var(--spacing-lg);">
                                    <div style="display: flex; align-items: center; gap: var(--spacing-sm); margin-bottom: var(--spacing-sm);">
                                        <span style="color: var(--success-green); font-size: 1.5rem;">👨‍💼</span>
                                        <strong>Expert Consultants</strong>
                                    </div>
                                    <p style="font-size: 0.875rem; color: var(--neutral-gray);">15+ years of experience in residential construction projects.</p>
                                </div>
                                
                                <div style="border-top: 1px solid var(--border-light); padding-top: var(--spacing-md);">
                                    <div style="background: var(--neutral-gray-light); padding: var(--spacing-md); border-radius: var(--border-radius); border-left: 4px solid var(--accent-orange);">
                                        <p style="font-style: italic; margin-bottom: var(--spacing-xs);">"Professional service that saved us from costly mistakes. Highly recommended!"</p>
                                        <div style="font-size: 0.875rem; color: var(--neutral-gray);">
                                            <strong>Priya S.</strong> - Bangalore
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card-footer">
                                <div style="text-align: center;">
                                    <p style="margin-bottom: var(--spacing-sm);"><strong>Need Help?</strong></p>
                                    <div style="display: flex; gap: var(--spacing-sm); justify-content: center;">
                                        <a href="tel:+************" class="btn btn-sm btn-secondary">Call Us</a>
                                        <a href="https://wa.me/************" class="btn btn-sm btn-primary" target="_blank">WhatsApp</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script src="assets/js/main.js"></script>
    <script>
        // Set minimum date to tomorrow
        document.addEventListener('DOMContentLoaded', function() {
            const dateInput = document.getElementById('preferred-date');
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            dateInput.min = tomorrow.toISOString().split('T')[0];
            
            // Add footer
            const footer = document.createElement('footer');
            footer.className = 'site-footer';
            footer.innerHTML = `
                <div class="container">
                    <div class="footer-content">
                        <div class="footer-section">
                            <h3>BALLERY.IN</h3>
                            <p>Your trusted on-demand construction expert. Professional consultations for residential projects across India.</p>
                        </div>
                        <div class="footer-section">
                            <h3>Quick Links</h3>
                            <ul class="footer-links">
                                <li><a href="services.html">Our Services</a></li>
                                <li><a href="pricing.html">Pricing</a></li>
                                <li><a href="book-consultation.html">Book Consultation</a></li>
                                <li><a href="about.html">About Us</a></li>
                                <li><a href="contact.html">Contact</a></li>
                            </ul>
                        </div>
                        <div class="footer-section">
                            <h3>Get In Touch</h3>
                            <div class="contact-info">
                                <span class="contact-icon">📞</span>
                                <a href="tel:+************">+91 98765 43210</a>
                            </div>
                            <div class="contact-info">
                                <span class="contact-icon">📧</span>
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                            </div>
                        </div>
                    </div>
                    <div class="footer-bottom">
                        <p>&copy; ${new Date().getFullYear()} BALLERY.IN. All rights reserved.</p>
                    </div>
                </div>
            `;
            document.body.appendChild(footer);
        });
    </script>
</body>
</html>
