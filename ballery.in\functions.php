<?php
/**
 * BALLERY.IN Theme Functions
 * 
 * @package BALLERY.IN
 * @version 1.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function ballery_theme_setup() {
    // Add theme support for various features
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    add_theme_support('custom-logo');
    add_theme_support('responsive-embeds');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'ballery'),
        'footer' => __('Footer Menu', 'ballery'),
    ));
    
    // Set content width
    if (!isset($content_width)) {
        $content_width = 1200;
    }
}
add_action('after_setup_theme', 'ballery_theme_setup');

/**
 * Enqueue Scripts and Styles
 */
function ballery_scripts() {
    // Main stylesheet
    wp_enqueue_style('ballery-style', get_stylesheet_uri(), array(), '1.0.0');
    
    // Google Fonts (already loaded in header.php, but keeping for fallback)
    wp_enqueue_style('ballery-fonts', 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap', array(), null);
    
    // Main JavaScript (functionality is in footer.php for now)
    wp_enqueue_script('ballery-main', get_template_directory_uri() . '/assets/js/main.js', array(), '1.0.0', true);
    
    // Localize script for AJAX
    wp_localize_script('ballery-main', 'ballery_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('ballery_nonce'),
    ));
}
add_action('wp_enqueue_scripts', 'ballery_scripts');

/**
 * Custom Post Types
 */
function ballery_custom_post_types() {
    // Testimonials
    register_post_type('testimonial', array(
        'labels' => array(
            'name' => 'Testimonials',
            'singular_name' => 'Testimonial',
            'add_new' => 'Add New Testimonial',
            'add_new_item' => 'Add New Testimonial',
            'edit_item' => 'Edit Testimonial',
            'new_item' => 'New Testimonial',
            'view_item' => 'View Testimonial',
            'search_items' => 'Search Testimonials',
            'not_found' => 'No testimonials found',
            'not_found_in_trash' => 'No testimonials found in trash'
        ),
        'public' => false,
        'show_ui' => true,
        'show_in_menu' => true,
        'supports' => array('title', 'editor', 'thumbnail'),
        'menu_icon' => 'dashicons-format-quote',
    ));
    
    // Team Members
    register_post_type('team_member', array(
        'labels' => array(
            'name' => 'Team Members',
            'singular_name' => 'Team Member',
            'add_new' => 'Add New Team Member',
            'add_new_item' => 'Add New Team Member',
            'edit_item' => 'Edit Team Member',
            'new_item' => 'New Team Member',
            'view_item' => 'View Team Member',
            'search_items' => 'Search Team Members',
            'not_found' => 'No team members found',
            'not_found_in_trash' => 'No team members found in trash'
        ),
        'public' => false,
        'show_ui' => true,
        'show_in_menu' => true,
        'supports' => array('title', 'editor', 'thumbnail'),
        'menu_icon' => 'dashicons-groups',
    ));
    
    // Portfolio Projects
    register_post_type('portfolio', array(
        'labels' => array(
            'name' => 'Portfolio',
            'singular_name' => 'Project',
            'add_new' => 'Add New Project',
            'add_new_item' => 'Add New Project',
            'edit_item' => 'Edit Project',
            'new_item' => 'New Project',
            'view_item' => 'View Project',
            'search_items' => 'Search Projects',
            'not_found' => 'No projects found',
            'not_found_in_trash' => 'No projects found in trash'
        ),
        'public' => false,
        'show_ui' => true,
        'show_in_menu' => true,
        'supports' => array('title', 'editor', 'thumbnail'),
        'menu_icon' => 'dashicons-portfolio',
    ));
}
add_action('init', 'ballery_custom_post_types');

/**
 * Custom Meta Fields
 */
function ballery_add_meta_boxes() {
    // Testimonial meta
    add_meta_box(
        'testimonial_details',
        'Testimonial Details',
        'ballery_testimonial_meta_callback',
        'testimonial',
        'normal',
        'high'
    );
    
    // Team member meta
    add_meta_box(
        'team_member_details',
        'Team Member Details',
        'ballery_team_member_meta_callback',
        'team_member',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'ballery_add_meta_boxes');

function ballery_testimonial_meta_callback($post) {
    wp_nonce_field('ballery_testimonial_meta', 'ballery_testimonial_nonce');
    
    $client_name = get_post_meta($post->ID, '_client_name', true);
    $client_location = get_post_meta($post->ID, '_client_location', true);
    $rating = get_post_meta($post->ID, '_rating', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="client_name">Client Name</label></th>';
    echo '<td><input type="text" id="client_name" name="client_name" value="' . esc_attr($client_name) . '" class="regular-text" /></td></tr>';
    echo '<tr><th><label for="client_location">Client Location</label></th>';
    echo '<td><input type="text" id="client_location" name="client_location" value="' . esc_attr($client_location) . '" class="regular-text" /></td></tr>';
    echo '<tr><th><label for="rating">Rating (1-5)</label></th>';
    echo '<td><select id="rating" name="rating">';
    for ($i = 1; $i <= 5; $i++) {
        echo '<option value="' . $i . '"' . selected($rating, $i, false) . '>' . $i . ' Star' . ($i > 1 ? 's' : '') . '</option>';
    }
    echo '</select></td></tr>';
    echo '</table>';
}

function ballery_team_member_meta_callback($post) {
    wp_nonce_field('ballery_team_member_meta', 'ballery_team_member_nonce');
    
    $position = get_post_meta($post->ID, '_position', true);
    $experience = get_post_meta($post->ID, '_experience', true);
    $qualifications = get_post_meta($post->ID, '_qualifications', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="position">Position</label></th>';
    echo '<td><input type="text" id="position" name="position" value="' . esc_attr($position) . '" class="regular-text" /></td></tr>';
    echo '<tr><th><label for="experience">Years of Experience</label></th>';
    echo '<td><input type="number" id="experience" name="experience" value="' . esc_attr($experience) . '" class="small-text" /></td></tr>';
    echo '<tr><th><label for="qualifications">Qualifications</label></th>';
    echo '<td><textarea id="qualifications" name="qualifications" rows="3" class="large-text">' . esc_textarea($qualifications) . '</textarea></td></tr>';
    echo '</table>';
}

/**
 * Save Meta Fields
 */
function ballery_save_meta_fields($post_id) {
    // Testimonial meta
    if (isset($_POST['ballery_testimonial_nonce']) && wp_verify_nonce($_POST['ballery_testimonial_nonce'], 'ballery_testimonial_meta')) {
        if (isset($_POST['client_name'])) {
            update_post_meta($post_id, '_client_name', sanitize_text_field($_POST['client_name']));
        }
        if (isset($_POST['client_location'])) {
            update_post_meta($post_id, '_client_location', sanitize_text_field($_POST['client_location']));
        }
        if (isset($_POST['rating'])) {
            update_post_meta($post_id, '_rating', intval($_POST['rating']));
        }
    }
    
    // Team member meta
    if (isset($_POST['ballery_team_member_nonce']) && wp_verify_nonce($_POST['ballery_team_member_nonce'], 'ballery_team_member_meta')) {
        if (isset($_POST['position'])) {
            update_post_meta($post_id, '_position', sanitize_text_field($_POST['position']));
        }
        if (isset($_POST['experience'])) {
            update_post_meta($post_id, '_experience', intval($_POST['experience']));
        }
        if (isset($_POST['qualifications'])) {
            update_post_meta($post_id, '_qualifications', sanitize_textarea_field($_POST['qualifications']));
        }
    }
}
add_action('save_post', 'ballery_save_meta_fields');

/**
 * AJAX Handler for Contact Forms
 */
function ballery_handle_contact_form() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'ballery_nonce')) {
        wp_die('Security check failed');
    }
    
    $name = sanitize_text_field($_POST['name']);
    $email = sanitize_email($_POST['email']);
    $phone = sanitize_text_field($_POST['phone']);
    $message = sanitize_textarea_field($_POST['message']);
    
    // Send email notification
    $to = get_option('admin_email');
    $subject = 'New Contact Form Submission - BALLERY.IN';
    $body = "Name: $name\nEmail: $email\nPhone: $phone\nMessage: $message";
    $headers = array('Content-Type: text/html; charset=UTF-8');
    
    if (wp_mail($to, $subject, $body, $headers)) {
        wp_send_json_success('Message sent successfully!');
    } else {
        wp_send_json_error('Failed to send message. Please try again.');
    }
}
add_action('wp_ajax_ballery_contact_form', 'ballery_handle_contact_form');
add_action('wp_ajax_nopriv_ballery_contact_form', 'ballery_handle_contact_form');

/**
 * Helper Functions
 */
function ballery_get_testimonials($limit = 3) {
    $testimonials = get_posts(array(
        'post_type' => 'testimonial',
        'posts_per_page' => $limit,
        'post_status' => 'publish',
        'orderby' => 'menu_order',
        'order' => 'ASC'
    ));
    
    return $testimonials;
}

function ballery_get_team_members($limit = -1) {
    $team_members = get_posts(array(
        'post_type' => 'team_member',
        'posts_per_page' => $limit,
        'post_status' => 'publish',
        'orderby' => 'menu_order',
        'order' => 'ASC'
    ));
    
    return $team_members;
}

function ballery_get_portfolio_projects($limit = 6) {
    $projects = get_posts(array(
        'post_type' => 'portfolio',
        'posts_per_page' => $limit,
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'DESC'
    ));
    
    return $projects;
}

/**
 * Security Enhancements
 */
function ballery_security_headers() {
    if (!is_admin()) {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('X-XSS-Protection: 1; mode=block');
    }
}
add_action('send_headers', 'ballery_security_headers');

/**
 * Performance Optimizations
 */
function ballery_optimize_performance() {
    // Remove unnecessary WordPress features
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wp_shortlink_wp_head');
    
    // Disable emoji scripts
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
}
add_action('init', 'ballery_optimize_performance');

?>
