# BALLERY.IN - Git Ignore File

# Operating System Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# IDE and Editor Files
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Dependency directories
node_modules/
bower_components/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# WordPress specific
wp-config.php
wp-content/uploads/
wp-content/cache/
wp-content/backup-db/
wp-content/advanced-cache.php
wp-content/wp-cache-config.php
wp-content/blogs.dir/
wp-content/upgrade/
wp-content/backup-db/
wp-content/backups/
wp-content/blogs.dir/
wp-content/cache/
wp-content/uploads/
wp-content/updraft/

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Compressed files
*.zip
*.tar.gz
*.rar

# Build directories
dist/
build/
public/

# Cache directories
.cache/
.parcel-cache/

# Local development
.local/
.env.local

# Database
*.sql
*.sqlite
*.db

# Images (if you want to exclude large image files)
# *.jpg
# *.jpeg
# *.png
# *.gif
# *.webp

# Keep important files
!assets/images/.gitkeep
