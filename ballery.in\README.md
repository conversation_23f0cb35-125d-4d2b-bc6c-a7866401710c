# BALLERY.IN - Professional Construction Consultation Website

A modern, responsive WordPress theme for BALLERY.IN - India's premier on-demand construction consultation platform.

## 🏗️ Project Overview

BALLERY.IN provides professional construction consultation services through a "PMC-Lite" model, offering expert advice for residential construction projects across India. This website serves as the primary platform for service booking and client engagement.

## ✨ Key Features

### Design & User Experience
- **Mobile-First Responsive Design** - Optimized for all devices
- **Professional Color Palette** - Blues and grays with orange accent for CTAs
- **Clean Typography** - Inter for body text, Poppins for headings
- **Accessibility Compliant** - WCAG 2.1 standards
- **Performance Optimized** - <3 second load times

### Core Functionality
- **Dynamic Service Booking** - Tab-based interface with dynamic form fields
- **WordPress Integration** - Custom post types for testimonials, team, portfolio
- **Database-Compliant Booking** - No external embeds, all data stored locally
- **SEO Optimized** - Structured data and meta optimization
- **Security Enhanced** - Headers and input sanitization

### Pages Included
1. **Homepage** - Hero section, testimonials, services overview
2. **Services** - Tab-based virtual and site visit services
3. **Pricing** - Multi-column pricing table with packages
4. **Book Consultation** - Dynamic booking form with file upload
5. **About Us** - Team, mission, portfolio, model comparison
6. **Contact** - Contact form, office info, Google Maps integration

## 🚀 Installation Instructions

### Prerequisites
- WordPress 5.0 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher

### Setup Steps

1. **Upload Theme Files**
   ```bash
   # Upload all files to your WordPress theme directory
   /wp-content/themes/ballery-in/
   ```

2. **Activate Theme**
   - Go to WordPress Admin → Appearance → Themes
   - Activate "BALLERY.IN Professional Theme"

3. **Create Required Pages**
   Create the following pages with exact slugs:
   - `services` (use page-services.php template)
   - `pricing` (use page-pricing.php template)
   - `book-consultation` (use page-book-consultation.php template)
   - `about` (use page-about.php template)
   - `contact` (use page-contact.php template)

4. **Configure Menus**
   - Go to Appearance → Menus
   - Create a menu with the pages above
   - Assign to "Primary Menu" location

5. **Add Content**
   - Upload team member photos to `/assets/images/`
   - Add testimonials via WordPress admin
   - Configure contact information in footer.php

## 📁 File Structure

```
ballery.in/
├── style.css                 # Main stylesheet with WordPress theme header
├── index.php                 # Homepage template
├── header.php                # Site header and navigation
├── footer.php                # Site footer and JavaScript
├── functions.php             # WordPress theme functions
├── page-services.php         # Services page template
├── page-pricing.php          # Pricing page template
├── page-book-consultation.php # Booking form template
├── page-about.php            # About us page template
├── page-contact.php          # Contact page template
├── assets/
│   └── js/
│       └── main.js           # Additional JavaScript functionality
└── README.md                 # This file
```

## 🎨 Customization Guide

### Colors
Update CSS variables in `style.css`:
```css
:root {
    --primary-blue: #2c5aa0;
    --accent-orange: #ff6b35;  /* CTA color - use sparingly */
    --neutral-gray: #6c757d;
}
```

### Typography
Fonts are loaded from Google Fonts in `header.php`:
- **Body Text**: Inter (400, 500, 600, 700)
- **Headings**: Poppins (400, 500, 600, 700)

### Contact Information
Update contact details in:
- `footer.php` - Footer contact section
- `page-contact.php` - Contact page details
- `functions.php` - Email notifications

## 🔧 WordPress Integration

### Custom Post Types
The theme creates three custom post types:
- **Testimonials** - Client reviews with meta fields
- **Team Members** - Staff profiles with qualifications
- **Portfolio** - Project showcase

### Booking System Integration
The booking form is designed to work with WordPress booking plugins:
- **Recommended**: Fluent Booking, Amelia, or similar
- **Requirement**: Must store data in WordPress database
- **Integration Point**: `page-book-consultation.php`

### Form Handling
Forms use WordPress AJAX for submission:
- Contact forms → `ballery_handle_contact_form()`
- Booking forms → Ready for plugin integration
- All forms include nonce security

## 📱 Responsive Breakpoints

- **Mobile**: < 576px
- **Tablet**: 576px - 768px
- **Desktop**: 768px - 1200px
- **Large Desktop**: > 1200px

## 🔒 Security Features

- Input sanitization and validation
- Nonce verification for forms
- Security headers (X-Frame-Options, etc.)
- SQL injection prevention
- XSS protection

## ⚡ Performance Optimizations

- Minified CSS and JavaScript
- Optimized images (WebP support)
- Lazy loading for images
- Reduced HTTP requests
- Gzip compression ready

## 🧪 Testing Checklist

### Functionality Testing
- [ ] Mobile menu toggle works
- [ ] Service type selection updates form fields
- [ ] All forms submit successfully
- [ ] Testimonial carousel auto-advances
- [ ] FAQ accordion opens/closes
- [ ] Tab system switches content

### Responsive Testing
- [ ] Mobile layout (< 576px)
- [ ] Tablet layout (576px - 768px)
- [ ] Desktop layout (> 768px)
- [ ] Navigation works on all devices

### Performance Testing
- [ ] Page load time < 3 seconds
- [ ] Images optimized and compressed
- [ ] CSS and JS minified
- [ ] No console errors

## 🚀 Deployment

### Pre-Launch Checklist
1. Update all placeholder content
2. Add real team photos and testimonials
3. Configure Google Maps embed
4. Set up contact form email delivery
5. Install and configure booking plugin
6. Test all forms and functionality
7. Optimize images for web
8. Set up SSL certificate
9. Configure caching
10. Submit sitemap to search engines

### Recommended Plugins
- **Booking**: Fluent Booking or Amelia
- **SEO**: Yoast SEO or RankMath
- **Security**: Wordfence
- **Performance**: WP Rocket or W3 Total Cache
- **Backup**: UpdraftPlus

## 📞 Support

For technical support or customization requests:
- **Email**: <EMAIL>
- **Phone**: +91 98765 43210
- **WhatsApp**: Available for urgent issues

## 📄 License

This theme is proprietary to BALLERY.IN. All rights reserved.

---

**Built with ❤️ for BALLERY.IN - Your Trusted Construction Partner**
