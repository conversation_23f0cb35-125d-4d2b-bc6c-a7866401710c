</main>

<footer class="site-footer">
    <div class="container">
        <div class="footer-content">
            <!-- Company Info -->
            <div class="footer-section">
                <h3>BALLERY.IN</h3>
                <p>Your trusted on-demand construction expert. Professional consultations for residential projects across India.</p>
                <p>Quality assured, costs controlled.</p>
            </div>
            
            <!-- Quick Links -->
            <div class="footer-section">
                <h3>Quick Links</h3>
                <ul class="footer-links">
                    <li><a href="<?php echo home_url('/services'); ?>">Our Services</a></li>
                    <li><a href="<?php echo home_url('/pricing'); ?>">Pricing</a></li>
                    <li><a href="<?php echo home_url('/book-consultation'); ?>">Book Consultation</a></li>
                    <li><a href="<?php echo home_url('/about'); ?>">About Us</a></li>
                    <li><a href="<?php echo home_url('/contact'); ?>">Contact</a></li>
                </ul>
            </div>
            
            <!-- Services -->
            <div class="footer-section">
                <h3>Services</h3>
                <ul class="footer-links">
                    <li><a href="<?php echo home_url('/services#virtual'); ?>">Virtual Consultations</a></li>
                    <li><a href="<?php echo home_url('/services#site-visit'); ?>">Site Visits</a></li>
                    <li><a href="<?php echo home_url('/services'); ?>">Plan Reviews</a></li>
                    <li><a href="<?php echo home_url('/services'); ?>">Quality Inspections</a></li>
                    <li><a href="<?php echo home_url('/services'); ?>">Cost Advisory</a></li>
                </ul>
            </div>
            
            <!-- Contact Info -->
            <div class="footer-section">
                <h3>Get In Touch</h3>
                <div class="contact-info">
                    <span class="contact-icon">📞</span>
                    <a href="tel:+************">+91 98765 43210</a>
                </div>
                <div class="contact-info">
                    <span class="contact-icon">📧</span>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="contact-info">
                    <span class="contact-icon">💬</span>
                    <a href="https://wa.me/************" target="_blank" rel="noopener">WhatsApp Us</a>
                </div>
                <div class="contact-info">
                    <span class="contact-icon">📍</span>
                    <span>Bangalore, Karnataka, India</span>
                </div>
            </div>
        </div>
        
        <div class="footer-bottom">
            <p>&copy; <?php echo date('Y'); ?> BALLERY.IN. All rights reserved. | <a href="<?php echo home_url('/privacy-policy'); ?>">Privacy Policy</a> | <a href="<?php echo home_url('/terms-of-service'); ?>">Terms of Service</a></p>
        </div>
    </div>
</footer>

<?php wp_footer(); ?>

<!-- JavaScript for Mobile Menu and Interactive Features -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mobile Menu Toggle
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const mainNav = document.getElementById('main-nav');
    
    if (mobileMenuToggle && mainNav) {
        mobileMenuToggle.addEventListener('click', function() {
            mainNav.classList.toggle('active');
            const isOpen = mainNav.classList.contains('active');
            mobileMenuToggle.setAttribute('aria-expanded', isOpen);
            mobileMenuToggle.innerHTML = isOpen ? '<span>✕</span>' : '<span>☰</span>';
        });
    }
    
    // Header Scroll Effect
    const siteHeader = document.getElementById('site-header');
    let lastScrollTop = 0;
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > 100) {
            siteHeader.classList.add('scrolled');
        } else {
            siteHeader.classList.remove('scrolled');
        }
        
        lastScrollTop = scrollTop;
    });
    
    // Smooth Scrolling for Anchor Links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // FAQ Accordion
    document.querySelectorAll('.faq-question').forEach(question => {
        question.addEventListener('click', function() {
            const faqItem = this.parentElement;
            const isActive = faqItem.classList.contains('active');
            
            // Close all FAQ items
            document.querySelectorAll('.faq-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Open clicked item if it wasn't active
            if (!isActive) {
                faqItem.classList.add('active');
            }
        });
    });
    
    // Tab Functionality
    document.querySelectorAll('.tab-button').forEach(button => {
        button.addEventListener('click', function() {
            const tabsContainer = this.closest('.tabs-container');
            const targetTab = this.getAttribute('data-tab');
            
            // Remove active class from all buttons and content
            tabsContainer.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            tabsContainer.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            // Add active class to clicked button and corresponding content
            this.classList.add('active');
            const targetContent = tabsContainer.querySelector(`[data-tab-content="${targetTab}"]`);
            if (targetContent) {
                targetContent.classList.add('active');
            }
        });
    });
    
    // Form Validation and Dynamic Service Selection
    const serviceTypeSelect = document.getElementById('service-type');
    const serviceOptionsContainer = document.getElementById('service-options');
    
    if (serviceTypeSelect && serviceOptionsContainer) {
        serviceTypeSelect.addEventListener('change', function() {
            const selectedType = this.value;
            updateServiceOptions(selectedType);
        });
    }
    
    function updateServiceOptions(serviceType) {
        const virtualServices = [
            'Drawing Evaluation',
            'Agreement Verification',
            'BoQ Preparation',
            'Structural Consultation',
            'Material Specification Review',
            'Cost Estimation'
        ];
        
        const siteVisitServices = [
            'Site Marking Check',
            'Steel Reinforcement Check',
            'Concreting Check',
            'Measurements & Billing Check',
            'Quality Inspection',
            'Safety Audit'
        ];
        
        const services = serviceType === 'virtual' ? virtualServices : siteVisitServices;
        
        serviceOptionsContainer.innerHTML = '';
        services.forEach(service => {
            const checkboxDiv = document.createElement('div');
            checkboxDiv.className = 'form-checkbox';
            checkboxDiv.innerHTML = `
                <input type="checkbox" id="service-${service.toLowerCase().replace(/\s+/g, '-')}" name="services[]" value="${service}">
                <label for="service-${service.toLowerCase().replace(/\s+/g, '-')}">${service}</label>
            `;
            serviceOptionsContainer.appendChild(checkboxDiv);
        });
    }
    
    // Testimonial Carousel
    const testimonialTrack = document.querySelector('.testimonial-track');
    const testimonialDots = document.querySelectorAll('.carousel-dot');
    let currentSlide = 0;
    
    if (testimonialTrack && testimonialDots.length > 0) {
        testimonialDots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                currentSlide = index;
                updateCarousel();
            });
        });
        
        function updateCarousel() {
            testimonialTrack.style.transform = `translateX(-${currentSlide * 100}%)`;
            testimonialDots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentSlide);
            });
        }
        
        // Auto-advance carousel
        setInterval(() => {
            currentSlide = (currentSlide + 1) % testimonialDots.length;
            updateCarousel();
        }, 5000);
    }
});
</script>

</body>
</html>
