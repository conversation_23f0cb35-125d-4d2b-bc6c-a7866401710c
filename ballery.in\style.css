/*
Theme Name: BALLERY.IN Professional Theme
Description: Custom WordPress theme for BALLERY.IN - PMC-Lite Construction Consultation Platform
Version: 1.0
Author: BALLERY.IN Team
*/

/* ===== CSS RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #2c3e50;
    background-color: #ffffff;
}

/* ===== COLOR PALETTE ===== */
:root {
    /* Primary Colors - Blues & Grays */
    --primary-blue: #2c5aa0;
    --primary-blue-dark: #1e3d6f;
    --primary-blue-light: #4a7bc8;
    --neutral-gray: #6c757d;
    --neutral-gray-light: #f8f9fa;
    --neutral-gray-dark: #495057;
    
    /* Accent Color - Exclusively for CTAs */
    --accent-orange: #ff6b35;
    --accent-orange-hover: #e55a2b;
    
    /* Supporting Colors */
    --white: #ffffff;
    --black: #000000;
    --success-green: #28a745;
    --border-light: #e9ecef;
    
    /* Typography */
    --font-body: 'Inter', sans-serif;
    --font-heading: 'Poppins', sans-serif;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-xxl: 4rem;
    
    /* Container */
    --container-max-width: 1200px;
    --container-padding: 1rem;
    
    /* Border Radius */
    --border-radius: 8px;
    --border-radius-lg: 12px;
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-blue-dark);
}

h1 {
    font-size: 2.5rem;
    font-weight: 700;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.5rem;
}

h4 {
    font-size: 1.25rem;
}

p {
    margin-bottom: var(--spacing-sm);
    color: var(--neutral-gray-dark);
}

a {
    color: var(--primary-blue);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--primary-blue-dark);
}

/* ===== LAYOUT COMPONENTS ===== */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

.section {
    padding: var(--spacing-xxl) 0;
}

.section-sm {
    padding: var(--spacing-xl) 0;
}

.grid {
    display: grid;
    gap: var(--spacing-lg);
}

.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.flex {
    display: flex;
    gap: var(--spacing-md);
}

.flex-center {
    justify-content: center;
    align-items: center;
}

.flex-between {
    justify-content: space-between;
    align-items: center;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

/* ===== BUTTONS & CTAs ===== */
.btn {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius);
    font-family: var(--font-body);
    font-size: 1rem;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    line-height: 1.5;
}

/* Primary CTA Button - Accent Color Only */
.btn-primary {
    background-color: var(--accent-orange);
    color: var(--white);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background-color: var(--accent-orange-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: var(--white);
}

/* Secondary Button - Vibrant Style */
.btn-secondary {
    background-color: #4a90e2;
    color: var(--white);
    border: 2px solid #4a90e2;
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
    background-color: #357abd;
    border-color: #357abd;
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Button Sizes */
.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1.125rem;
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 0.875rem;
}

/* ===== CARDS ===== */
.card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    padding: var(--spacing-lg);
    transition: all 0.3s ease;
    border: 1px solid var(--border-light);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    margin-bottom: var(--spacing-md);
}

.card-title {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-xs);
}

.card-subtitle {
    color: var(--neutral-gray);
    font-size: 0.875rem;
}

.card-body {
    margin-bottom: var(--spacing-md);
}

.card-footer {
    border-top: 1px solid var(--border-light);
    padding-top: var(--spacing-md);
}

/* ===== NAVIGATION ===== */
.site-header {
    background: var(--white);
    box-shadow: var(--shadow-sm);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.site-header.scrolled {
    box-shadow: var(--shadow-md);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
}

.site-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-blue-dark);
    text-decoration: none;
    transition: all 0.3s ease;
}

.site-logo:hover {
    color: var(--primary-blue);
    transform: scale(1.05);
}

.site-logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--accent-orange) 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.2rem;
    box-shadow: var(--shadow-sm);
}

.site-logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.main-nav {
    display: flex;
    list-style: none;
    gap: var(--spacing-lg);
    align-items: center;
}

.main-nav li {
    display: flex;
    align-items: center;
}

.main-nav a {
    color: var(--neutral-gray-dark);
    font-weight: 500;
    padding: var(--spacing-xs) 0;
    position: relative;
    transition: color 0.3s ease;
}

.main-nav a:hover,
.main-nav a.active {
    color: var(--primary-blue);
}

.main-nav a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-orange);
    transition: width 0.3s ease;
}

.main-nav a:hover::after,
.main-nav a.active::after {
    width: 100%;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--primary-blue-dark);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-menu-toggle:hover {
    background: var(--neutral-gray-light);
    color: var(--primary-blue);
}

.mobile-menu-toggle:focus {
    outline: 2px solid var(--accent-orange);
    outline-offset: 2px;
}

/* ===== HERO SECTION ===== */
.hero {
    background: linear-gradient(135deg, var(--primary-blue-dark) 0%, var(--primary-blue) 100%);
    color: var(--white);
    padding: calc(var(--spacing-xxl) + 60px) 0 var(--spacing-xxl);
    position: relative;
    overflow: hidden;
    min-height: 70vh;
    display: flex;
    align-items: center;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero h1 {
    color: var(--white);
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    line-height: 1.1;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-xl);
    color: #ffffff;
    font-weight: 400;
    line-height: 1.5;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* Page subtitle styling for Services and About pages */
.page-subtitle {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-xl);
    color: var(--black);
    font-weight: 400;
    line-height: 1.5;
    opacity: 0.9;
}

.hero-cta {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

/* ===== SOCIAL PROOF BAR ===== */
.social-proof {
    background: var(--neutral-gray-light);
    padding: var(--spacing-lg) 0;
    text-align: center;
}

.social-proof-content {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-xl);
    flex-wrap: wrap;
}

.social-proof-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-blue-dark);
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--neutral-gray);
    margin-top: var(--spacing-xs);
}

/* ===== TESTIMONIALS ===== */
.testimonials {
    background: var(--white);
    padding: var(--spacing-xxl) 0;
}

.testimonials-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.testimonial-carousel {
    position: relative;
    overflow: hidden;
}

.testimonial-track {
    display: flex;
    transition: transform 0.5s ease;
}

.testimonial-slide {
    min-width: 100%;
    padding: 0 var(--spacing-md);
}

.testimonial {
    background: var(--neutral-gray-light);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.testimonial-quote {
    font-size: 1.125rem;
    font-style: italic;
    margin-bottom: var(--spacing-lg);
    color: var(--neutral-gray-dark);
    line-height: 1.6;
}

.testimonial-author {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
}

.author-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.author-info h4 {
    margin-bottom: var(--spacing-xs);
    color: var(--primary-blue-dark);
}

.author-location {
    color: var(--neutral-gray);
    font-size: 0.875rem;
}

.carousel-controls {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
}

.carousel-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--border-light);
    border: none;
    cursor: pointer;
    transition: background 0.3s ease;
}

.carousel-dot.active {
    background: var(--accent-orange);
}

/* ===== SERVICES SECTION ===== */
.services-snapshot {
    padding: var(--spacing-xxl) 0;
    background: var(--neutral-gray-light);
}

.services-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.service-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    border: 1px solid var(--border-light);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.service-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-md);
    background: var(--primary-blue-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--white);
}

.service-card h3 {
    margin-bottom: var(--spacing-md);
}

.service-card p {
    margin-bottom: var(--spacing-lg);
    color: var(--neutral-gray);
}

/* ===== HOW IT WORKS ===== */
.how-it-works {
    padding: var(--spacing-xxl) 0;
    background: var(--white);
}

.how-it-works-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.steps-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    position: relative;
}

.step {
    text-align: center;
    position: relative;
}

.step-number {
    width: 60px;
    height: 60px;
    background: var(--accent-orange);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto var(--spacing-md);
}

.step h3 {
    margin-bottom: var(--spacing-sm);
}

.step p {
    color: var(--neutral-gray);
}

/* ===== FORMS ===== */
.form-section {
    background: var(--neutral-gray-light);
    padding: var(--spacing-xxl) 0;
}

.form-container {
    max-width: 600px;
    margin: 0 auto;
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.form-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--primary-blue-dark);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-sm);
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius);
    font-family: var(--font-body);
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.form-checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
}

.form-checkbox {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.form-checkbox input[type="checkbox"] {
    width: auto;
}

.form-file {
    border: 2px dashed var(--border-light);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    text-align: center;
    transition: border-color 0.3s ease;
}

.form-file:hover {
    border-color: var(--primary-blue);
}

.form-submit {
    width: 100%;
    margin-top: var(--spacing-lg);
}

/* ===== TABS ===== */
.tabs-container {
    margin-bottom: var(--spacing-xl);
}

.tabs-nav {
    display: flex;
    border-bottom: 2px solid var(--border-light);
    margin-bottom: var(--spacing-lg);
}

.tab-button {
    background: none;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    font-family: var(--font-body);
    font-size: 1rem;
    font-weight: 500;
    color: var(--neutral-gray);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-button.active {
    color: var(--primary-blue);
    border-bottom-color: var(--accent-orange);
}

.tab-button:hover {
    color: var(--primary-blue);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* ===== PRICING TABLE ===== */
.pricing-section {
    padding: var(--spacing-xxl) 0;
    background: var(--white);
}

.pricing-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.pricing-table {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.pricing-column {
    background: var(--white);
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    position: relative;
    transition: all 0.3s ease;
}

.pricing-column.featured {
    border-color: var(--accent-orange);
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.pricing-column.featured::before {
    content: 'Most Popular';
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--accent-orange);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
}

.pricing-title {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-md);
    color: var(--primary-blue-dark);
}

.pricing-price {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: var(--spacing-xs);
}

.pricing-currency {
    font-size: 1.5rem;
    vertical-align: top;
}

.pricing-period {
    color: var(--neutral-gray);
    font-size: 0.875rem;
    margin-bottom: var(--spacing-lg);
}

.pricing-features {
    list-style: none;
    margin-bottom: var(--spacing-xl);
}

.pricing-features li {
    padding: var(--spacing-xs) 0;
    color: var(--neutral-gray-dark);
    position: relative;
    padding-left: var(--spacing-lg);
}

.pricing-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--success-green);
    font-weight: 700;
}

.savings-badge {
    background: var(--success-green);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: var(--spacing-md);
    display: inline-block;
}

/* ===== FAQ ACCORDION ===== */
.faq-section {
    background: var(--neutral-gray-light);
    padding: var(--spacing-xxl) 0;
}

.faq-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: var(--white);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.faq-question {
    width: 100%;
    background: none;
    border: none;
    padding: var(--spacing-lg);
    text-align: left;
    font-family: var(--font-body);
    font-size: 1.125rem;
    font-weight: 500;
    color: var(--primary-blue-dark);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background 0.3s ease;
}

.faq-question:hover {
    background: var(--neutral-gray-light);
}

.faq-icon {
    font-size: 1.5rem;
    transition: transform 0.3s ease;
}

.faq-item.active .faq-icon {
    transform: rotate(45deg);
}

.faq-answer {
    padding: 0 var(--spacing-lg) var(--spacing-lg);
    color: var(--neutral-gray-dark);
    line-height: 1.6;
    display: none;
}

.faq-item.active .faq-answer {
    display: block;
}

/* ===== FOOTER ===== */
.site-footer {
    background: var(--primary-blue-dark);
    color: var(--white);
    padding: var(--spacing-xxl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-section h3 {
    color: var(--white);
    margin-bottom: var(--spacing-md);
}

.footer-section p,
.footer-section a {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: var(--spacing-sm);
}

.footer-section a:hover {
    color: var(--white);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-xs);
}

.contact-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.contact-icon {
    width: 20px;
    height: 20px;
    opacity: 0.8;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: var(--spacing-lg);
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
}

/* ===== UTILITY CLASSES ===== */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }
.mb-4 { margin-bottom: var(--spacing-lg) !important; }
.mb-5 { margin-bottom: var(--spacing-xl) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--spacing-xs) !important; }
.mt-2 { margin-top: var(--spacing-sm) !important; }
.mt-3 { margin-top: var(--spacing-md) !important; }
.mt-4 { margin-top: var(--spacing-lg) !important; }
.mt-5 { margin-top: var(--spacing-xl) !important; }

/* ===== RESPONSIVE DESIGN - MOBILE FIRST ===== */

/* Small devices (landscape phones, 576px and up) */
@media (max-width: 575.98px) {
    :root {
        --container-padding: 1rem;
        --spacing-xxl: 3rem;
        --spacing-xl: 2rem;
    }

    h1 {
        font-size: 2rem;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.125rem;
    }

    .hero-cta {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
    }

    .main-nav {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--white);
        flex-direction: column;
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-lg);
        gap: var(--spacing-md);
        z-index: 999;
        border-top: 1px solid var(--border-light);
        animation: slideDown 0.3s ease-out;
    }

    .main-nav.active {
        display: flex;
    }

    .main-nav li {
        width: 100%;
    }

    .main-nav a {
        display: block;
        padding: var(--spacing-md) 0;
        text-align: center;
        border-bottom: 1px solid var(--border-light);
        font-size: 1.1rem;
    }

    .main-nav a:last-child {
        border-bottom: none;
    }

    .mobile-menu-toggle {
        display: flex !important;
    }

    .site-logo-icon {
        width: 32px;
        height: 32px;
        font-size: 1rem;
    }

    .site-logo-text {
        font-size: 1.25rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .steps-container {
        grid-template-columns: 1fr;
    }

    .pricing-table {
        grid-template-columns: 1fr;
    }

    .pricing-column.featured {
        transform: none;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .social-proof-content {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .testimonial-author {
        flex-direction: column;
        text-align: center;
    }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
    .hero h1 {
        font-size: 2.75rem;
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .pricing-table {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 768px) {
    .hero-cta {
        justify-content: center;
    }

    .services-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .steps-container {
        grid-template-columns: repeat(3, 1fr);
    }

    .pricing-table {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
    .container {
        padding: 0 var(--spacing-lg);
    }

    .hero h1 {
        font-size: 3.5rem;
    }

    .hero-subtitle {
        font-size: 1.375rem;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for keyboard navigation */
.btn:focus,
.form-input:focus,
.form-select:focus,
.form-textarea:focus,
.tab-button:focus,
.faq-question:focus {
    outline: 2px solid var(--accent-orange);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --shadow-sm: 0 2px 4px rgba(0,0,0,0.3);
        --shadow-md: 0 4px 6px rgba(0,0,0,0.3);
        --shadow-lg: 0 10px 15px rgba(0,0,0,0.3);
    }
}
